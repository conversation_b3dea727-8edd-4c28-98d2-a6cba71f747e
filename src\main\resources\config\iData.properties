﻿#iData指定客户端类型,用户不要随意修改
ClientType=jalor5
#是否https请求iData(true:https请求;false:http请求)
isHttpsRequest=true
#指定的连接超时值（以毫秒为单位），该值将在打开到此 URLConnection 引用的资源的通信链接时使用。global
#如果在建立连接之前超时期满，则会引发一个 java.net.SocketTimeoutException。
#超时时间为 0 表示无穷大超时。
ConnectTimeout = 20000
#将读超时设置为指定的超时，以毫秒为单位。用一个非零值指定在建立到资源的连接后从 Input 流读入时的超时时间。
#如果在数据可读取之前超时期满，则会引发一个 java.net.SocketTimeoutException。
#超时时间为 0 表示无穷大超时。 
ReadTimeout = 20000
#以下内容请不要修改
dev.url=http://idata3-beta.huawei.com/ws/
dev.soaservicesurlv1=http://idata3-beta.huawei.com/ws/soaservices/
sit.url=http://idata3-beta.huawei.com/ws/
sit.soaservicesurlv1=http://idata3-beta.huawei.com/ws/soaservices/
uat.url=http://idata3-beta.huawei.com/ws/
uat.soaservicesurlv1=http://idata3-beta.huawei.com/ws/soaservices/
perf.url=http://idata3-beta.huawei.com/ws/
perf.soaservicesurlv1=http://idata3-beta.huawei.com/ws/soaservices/
production.url=http://idata3.huawei.com/ws/
production.soaservicesurlv1=http://idata3.huawei.com/ws/soaservices/
